"""
Test suite for the enhanced Trafilatura scraper
Tests various news websites including Channel News Asia and Straits Times
"""
import unittest
import json
from trafilatura_scraper import NewsContentExtractor

class TestTrafilaturaScraper(unittest.TestCase):
    """Test cases for the enhanced Trafilatura scraper"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.extractor = NewsContentExtractor()
        
        # Test URLs for different news sites
        self.test_urls = {
            'straits_times': 'https://www.straitstimes.com/singapore',
            'channel_news_asia': 'https://www.channelnewsasia.com/singapore',
            'bbc_news': 'https://www.bbc.com/news',
            'reuters': 'https://www.reuters.com/world/',
        }
    
    def test_straits_times_extraction(self):
        """Test extraction from Straits Times Singapore page"""
        print("\n=== Testing Straits Times ===")
        url = self.test_urls['straits_times']
        
        results = self.extractor.scrape_url(url)
        self.assertNotIn('error', results, "Extraction should not fail")
        
        best_result = self.extractor.get_best_result(results)
        self.assertNotIn('error', best_result, "Should find valid extraction")
        
        content = best_result['content']
        self.assertIsInstance(content, dict, "Content should be a dictionary")
        
        # Check for expected fields
        if 'title' in content:
            print(f"Title: {content['title']}")
            self.assertIn('Singapore', content['title'], "Title should mention Singapore")
        
        if 'text' in content:
            text_length = len(content['text'])
            print(f"Text length: {text_length} characters")
            self.assertGreater(text_length, 100, "Should extract substantial text")
        
        print(f"Extraction method: {best_result['method_used']}")
        print("✓ Straits Times test passed")
    
    def test_channel_news_asia_extraction(self):
        """Test extraction from Channel News Asia Singapore page"""
        print("\n=== Testing Channel News Asia ===")
        url = self.test_urls['channel_news_asia']
        
        results = self.extractor.scrape_url(url)
        self.assertNotIn('error', results, "Extraction should not fail")
        
        best_result = self.extractor.get_best_result(results)
        self.assertNotIn('error', best_result, "Should find valid extraction")
        
        content = best_result['content']
        self.assertIsInstance(content, dict, "Content should be a dictionary")
        
        print(f"Extraction method: {best_result['method_used']}")
        
        # Print available content fields
        if isinstance(content, dict):
            print("Available fields:", list(content.keys()))
            if 'title' in content:
                print(f"Title: {content['title']}")
            if 'text' in content:
                print(f"Text length: {len(content['text'])} characters")
        
        print("✓ Channel News Asia test completed")
    
    def test_extraction_methods_comparison(self):
        """Compare different extraction methods"""
        print("\n=== Testing Extraction Methods ===")
        url = self.test_urls['straits_times']
        
        results = self.extractor.scrape_url(url)
        
        print("Available extraction methods:")
        for method, result in results.items():
            if method != 'error' and result:
                print(f"- {method}: {'✓' if result else '✗'}")
                
                if isinstance(result, dict):
                    if 'text' in result:
                        print(f"  Text length: {len(result['text'])} chars")
                    elif 'raw_text' in result:
                        print(f"  Raw text length: {len(result['raw_text'])} chars")
        
        print("✓ Method comparison completed")
    
    def test_metadata_extraction(self):
        """Test metadata extraction capabilities"""
        print("\n=== Testing Metadata Extraction ===")
        url = self.test_urls['straits_times']
        
        results = self.extractor.scrape_url(url)
        best_result = self.extractor.get_best_result(results)
        
        if 'error' not in best_result:
            content = best_result['content']
            
            metadata_fields = ['title', 'author', 'date', 'url', 'description']
            found_metadata = []
            
            for field in metadata_fields:
                if field in content and content[field]:
                    found_metadata.append(field)
                    print(f"{field}: {content[field]}")
            
            print(f"Found metadata fields: {found_metadata}")
            self.assertGreater(len(found_metadata), 0, "Should extract some metadata")
        
        print("✓ Metadata extraction test completed")
    
    def test_error_handling(self):
        """Test error handling with invalid URLs"""
        print("\n=== Testing Error Handling ===")
        
        invalid_urls = [
            'https://invalid-domain-that-does-not-exist.com',
            'https://httpstat.us/404',
            'not-a-url'
        ]
        
        for url in invalid_urls:
            print(f"Testing invalid URL: {url}")
            results = self.extractor.scrape_url(url)
            
            # Should either have error or no valid results
            if 'error' in results:
                print(f"  ✓ Properly handled error: {results['error']}")
            else:
                best_result = self.extractor.get_best_result(results)
                if 'error' in best_result:
                    print(f"  ✓ No valid extraction found")
        
        print("✓ Error handling test completed")

def run_comprehensive_test():
    """Run a comprehensive test of the scraper"""
    print("=" * 60)
    print("COMPREHENSIVE TRAFILATURA SCRAPER TEST")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestTrafilaturaScraper)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    # Run comprehensive test
    success = run_comprehensive_test()
    
    if success:
        print("\n🎉 All tests passed! The scraper is working well.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
