import asyncio
from crawl4ai import AsyncWebCrawler
from bs4 import BeautifulSoup, Comment
import re
import argparse
from urllib.parse import urlparse

def clean_html(html: str) -> str:
    """Aggressively clean HTML without losing visible text content."""
    soup = BeautifulSoup(html, "lxml")

    # Preserve title text (if any)
    title_text = ""
    if soup.title and soup.title.string:
        title_text = soup.title.string.strip()

    # Work primarily on the <body>. Fallback to whole doc if absent.
    fragment = soup.body or soup

    # Remove noisy elements
    for tag in fragment(["script", "style", "noscript", "iframe", "picture", "source", "svg", "canvas", "link", "meta", "header", "footer", "nav", "form", "aside"]):
        tag.decompose()

    # Remove comments
    for comment in fragment.find_all(string=lambda txt: isinstance(txt, Comment)):
        comment.extract()

    # Strip attributes except safe ones for <a> and <img>
    for tag in fragment.find_all():
        safe_attrs = {}
        if tag.name == "a" and tag.has_attr("href"):
            safe_attrs["href"] = tag["href"]
        if tag.name == "img":
            for attr in ("src", "alt"):
                if tag.has_attr(attr):
                    safe_attrs[attr] = tag[attr]
        tag.attrs = safe_attrs

    # Remove empty elements (div/span/p/section) after cleanup
    for tag in fragment.find_all(["div", "span", "p", "section", "article"]):
        if not tag.get_text(strip=True):
            tag.decompose()

    # Collapse whitespace (preserve line breaks between block elements for readability)
    html_str = re.sub(r"\s+", " ", str(fragment))

    if title_text and title_text not in html_str:
        html_str = f"<h1>{title_text}</h1> " + html_str

    return html_str.strip()

def extract_content(html: str) -> tuple:
    """Return tuple with:
    1. Plain text content
    2. List of links with text and href
    """
    soup = BeautifulSoup(html, "lxml")
    
    # Extract plain text
    text = soup.get_text("\n", strip=True)
    dedup = re.sub(r"(\n)+", "\n", text).strip()
    
    # Extract all links with their text
    links = []
    for a in soup.find_all("a", href=True):
        if a.text.strip():
            links.append({
                "text": a.text.strip(),
                "href": a["href"]
            })
    
    return dedup, links

async def crawl_url(url, depth=0, max_depth=1, max_links=5):
    """Crawl a URL and its linked pages up to specified depth"""
    results = {}
    
    # Configure crawler with politeness settings
    crawler = AsyncWebCrawler(
        timeout=10,
        navigation_timeout=30,
        headless=True,
        stealth=True
    )
    
    # Crawl main URL
    print(f"Crawling: {url} (depth {depth})")
    result = await crawler.arun(url)
    if not result:
        print(f"Failed to crawl {url}")
        return results
    
    raw_html = result.html
    cleaned_html = clean_html(raw_html)
    text, links = extract_content(cleaned_html)
    
    results[url] = {
        'text': text,
        'links': links
    }
    
    # Recursive crawling if not at max depth
    if depth < max_depth:
        same_domain_links = [
            link['href'] for link in links 
            if urlparse(link['href']).netloc == urlparse(url).netloc
        ][:max_links]
        
        for link_url in same_domain_links:
            if link_url not in results:  # Avoid duplicates
                child_results = await crawl_url(link_url, depth+1, max_depth, max_links)
                results.update(child_results)
                await asyncio.sleep(1)  # Be polite
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Web crawler with recursive crawling')
    parser.add_argument('url', help='Starting URL to crawl')
    parser.add_argument('--depth', type=int, default=0, help='Maximum crawl depth (default: 0)')
    parser.add_argument('--max_links', type=int, default=5, help='Max links per page to follow (default: 5)')
    args = parser.parse_args()
    
    print(f"Starting crawl for {args.url} with depth={args.depth}")
    results = asyncio.run(crawl_url(args.url, max_depth=args.depth, max_links=args.max_links))
    
    print("\n=== CRAWL RESULTS ===")
    for url, data in results.items():
        print(f"\n=== URL: {url} ===")
        print(f"\nText content ({len(data['text'])} chars):")
        print(data['text'][:500] + "..." if len(data['text']) > 500 else data['text'])
        
        print(f"\nLinks found ({len(data['links'])}):")
        for i, link in enumerate(data['links'][:5], 1):
            print(f"{i}. [{link['text']}]({link['href']})")
        if len(data['links']) > 5:
            print(f"... and {len(data['links']) - 5} more links")
